<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="$emit('confirm')"
    :showBtn="true"
    :title="title">
    <view class="popup-content restart">
      {{ message }}
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'ConfirmPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      default: '是否确定执行此操作？'
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.restart {
  padding: 40rpx 20rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
</style>
