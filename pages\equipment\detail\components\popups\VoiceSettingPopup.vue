<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="声音设置">
    <view class="popup-content">
      <u-slider
        :showValue="true"
        min="0"
        max="25"
        v-model="localValue">
      </u-slider>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'VoiceSettingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    value: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      localValue: 4
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.localValue = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.localValue);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 40rpx 20rpx;
}
</style>
