<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="编辑">
    <view class="popup-content">
      <view class="point-name">
        <view class="pop-item flex align-center">
          <view class="pop-label"> 名称： </view>
          <view class="pop-content">
            <u-input
              v-model="localEditForm.name"
              maxlength="12"
              placeholder="请输入点位名称"
              border="none">
            </u-input>
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 点位： </view>
          <view
            class="pop-content"
            @click="handleSelectPoint">
            <u-input
              v-model="localEditForm.placeName"
              placeholder="请选择点位"
              disabled
              disabledColor="#fff"
              border="none"
              suffixIcon="arrow-right"
              :suffixIconStyle="{ fontSize: 24, color: '#555555' }">
            </u-input>
          </view>
        </view>
      </view>

      <view class="point-msg">
        <view class="pop-item flex align-center">
          <view class="pop-label"> 点位名称： </view>
          <view class="pop-content">
            {{ pointDetail.placeName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 场景： </view>
          <view class="pop-content">
            {{ pointDetail.sceneNames || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 管理员： </view>
          <view class="pop-content">
            {{ pointDetail.adminName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 区域/线路： </view>
          <view class="pop-content">
            {{ pointDetail.regionName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 地址： </view>
          <view class="pop-content">
            {{ pointDetail.address || "-" }}
          </view>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'DeviceEditPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    },
    editForm: {
      type: Object,
      default: () => ({})
    },
    pointDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localEditForm: {}
    }
  },
  watch: {
    editForm: {
      handler(newVal) {
        this.localEditForm = { ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleConfirm() {
      if (!this.localEditForm.name) {
        this.$modal.showToast("设备名称必填！");
        return;
      }
      if (!this.localEditForm.placeId) {
        this.$modal.showToast("请先设置点位！");
        return;
      }
      this.$emit('confirm', this.localEditForm);
    },
    handleSelectPoint() {
      this.$tab.navigateTo(`/pages/point/point?type=1`);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 20rpx;
}

.pop-item {
  margin-bottom: 20rpx;
  
  .pop-label {
    width: 120rpx;
    font-size: 28rpx;
    color: #333;
  }
  
  .pop-content {
    flex: 1;
    font-size: 28rpx;
    color: #666;
  }
}

.point-name {
  margin-bottom: 30rpx;
}

.point-msg {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}
</style>
