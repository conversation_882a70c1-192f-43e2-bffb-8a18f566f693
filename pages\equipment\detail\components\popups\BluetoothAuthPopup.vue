<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="蓝牙授权">
    <view class="popup-content blueTe">
      <view class="info">
        <view>1）在蓝牙软件上输入授权码后可以通过蓝牙控制机器。</view>
        <view>2）授权码只能由机器管理员生成,必须在30分钟内使用,且只能使用一次。</view>
      </view>
      <view class="blueTe-msg">
        <view>机器编号：</view>
        <view>{{ deviceDetail.deviceId }}</view>
      </view>
      <view class="blueTe-msg">
        <view>授权码：</view>
        <view>
          <u--input
            placeholder="请输入内容"
            border="surround"
            v-model="localAuthCode"
            @input="handleAuthCodeChange">
          </u--input>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'BluetoothAuthPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    },
    authCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localAuthCode: ''
    }
  },
  watch: {
    authCode: {
      handler(newVal) {
        this.localAuthCode = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      if (!this.localAuthCode) {
        this.$modal.msg("请输入授权码！");
        return;
      }
      this.$emit('confirm', this.localAuthCode);
    },
    handleAuthCodeChange(value) {
      this.localAuthCode = value;
      this.$emit('updateAuthCode', value);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.blueTe {
  padding: 20rpx;
}

.info {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  
  view {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 10rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.blueTe-msg {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  > view:first-child {
    width: 120rpx;
    font-size: 28rpx;
    color: #333;
  }
  
  > view:last-child {
    flex: 1;
    font-size: 28rpx;
    color: #666;
  }
}
</style>
