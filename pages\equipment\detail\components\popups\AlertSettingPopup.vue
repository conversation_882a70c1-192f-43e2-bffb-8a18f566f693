<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="告警设置">
    <view class="popup-content">
      <u--form
        labelPosition="left"
        :model="localAlertData"
        :rules="tempNotRule"
        ref="tempNot">
        <view v-if="deviceDetail.deviceStatus && deviceDetail.deviceStatus.tempValue != '无温控仪'">
          <view class="not-tit">温度告警设置</view>
          <u-form-item
            label="最低告警温度(℃):"
            prop="tempMin"
            labelWidth="140">
            <u--input v-model="localAlertData.tempMin"></u--input>
          </u-form-item>
          <u-form-item
            label="最高告警温度(℃):"
            prop="tempMax"
            labelWidth="140">
            <u--input v-model="localAlertData.tempMax"></u--input>
          </u-form-item>
        </view>
        <view class="not-tit">库存告警设置</view>
        <u-form-item
          label="缺货率预警值(%):"
          prop="stockOutRate"
          labelWidth="140">
          <u--input v-model="localAlertData.stockOutRate"></u--input>
        </u-form-item>
        <u-form-item
          label="缺货种类预警值(种):"
          prop="stockOutGoodsNum"
          labelWidth="140">
          <u--input v-model="localAlertData.stockOutGoodsNum"></u--input>
        </u-form-item>
      </u--form>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'AlertSettingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    },
    alertData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localAlertData: {},
      tempNotRule: {
        tempMin: [
          {
            required: true,
            message: "请输入最低告警温度",
            trigger: ["blur", "change"],
          },
        ],
        tempMax: [
          {
            required: true,
            message: "请输入最高告警温度",
            trigger: ["blur", "change"],
          },
        ],
        stockOutRate: [
          {
            required: true,
            message: "请输入缺货率预警值",
            trigger: ["blur", "change"],
          },
        ],
        stockOutGoodsNum: [
          {
            required: true,
            message: "请输入缺货种类预警值",
            trigger: ["blur", "change"],
          },
        ],
      }
    }
  },
  watch: {
    alertData: {
      handler(newVal) {
        this.localAlertData = { ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async handleConfirm() {
      try {
        await this.$refs.tempNot.validate();
        if (this.localAlertData.tempMax < this.localAlertData.tempMin) {
          this.$modal.msg("最高温度不能低于最低温度！");
          return;
        }
        this.$emit('confirm', this.localAlertData);
      } catch (error) {
        console.log('表单验证失败:', error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 20rpx;
}

.not-tit {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0;
  text-align: center;
}
</style>
