<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="温度设置">
    <view class="popup-content temp">
      <view class="flex" style="margin-top: 10rpx">
        <view class="lab" style="margin-top: -10rpx">工作模式:</view>
        <u-radio-group
          v-model="localTempDetail.selTempWorkModel"
          placement="row">
          <u-radio
            :customStyle="{ marginBottom: '16rpx', marginLeft: '16rpx' }"
            v-for="(item, index) in tempWorkModels"
            :key="index"
            :label="item.name"
            :name="item.id">
          </u-radio>
        </u-radio-group>
      </view>

      <view class="flex" style="margin-top: 20rpx">
        <view class="lab" style="margin-top: -10rpx">温度分配:</view>
        <u-radio-group v-model="localTempType" placement="row">
          <u-radio
            :customStyle="{ marginBottom: '16rpx', marginLeft: '16rpx' }"
            v-for="(item, index) in tempTypeModels"
            :key="index"
            :label="item.name"
            :name="item.id">
          </u-radio>
        </u-radio-group>
      </view>

      <view class="tips">
        提示：请根据需求设置温度，饮料零食等商品建议温度设置范围 4~10℃
      </view>

      <view
        class="flex align-center"
        style="margin-top: 30rpx; margin-bottom: 20rpx"
        v-if="localTempType == '0'">
        <view class="lab" style="margin-top: 0">工作温度(℃):</view>
        <view style="margin-left: 20rpx">
          <u--input v-model="localTempDetail.target"></u--input>
        </view>
      </view>
      
      <view v-else>
        <view class="tips">
          提示：时段设置为X点至Y点，结束时间需大于起始时间
        </view>
        
        <!-- 时段1 -->
        <view class="flex align-center">
          <view class="lab">时段1:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('temp', 0)">
            <view class="time-box">{{ localTempDetail.start1 }}</view>
            <view class="lab to">至</view>
            <view class="time-box">{{ localTempDetail.end1 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="localTempDetail.target1"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>
        
        <!-- 时段2 -->
        <view class="flex align-center">
          <view class="lab">时段2:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('temp', 1)">
            <view class="time-box">{{ localTempDetail.start2 }}</view>
            <view class="lab to">至</view>
            <view class="time-box">{{ localTempDetail.end2 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="localTempDetail.target2"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>
        
        <!-- 时段3 -->
        <view class="flex align-center">
          <view class="lab">时段3:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('temp', 2)">
            <view class="time-box">{{ localTempDetail.start3 }}</view>
            <view class="lab to">至</view>
            <view class="time-box">{{ localTempDetail.end3 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="localTempDetail.target3"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>
      </view>
      
      <view style="height: 20rpx"></view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'TemperatureSettingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    tempDetail: {
      type: Object,
      default: () => ({})
    },
    tempType: {
      type: [String, Number],
      default: 0
    },
    tempWorkModels: {
      type: Array,
      default: () => []
    },
    tempTypeModels: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localTempDetail: {},
      localTempType: 0
    }
  },
  watch: {
    tempDetail: {
      handler(newVal) {
        this.localTempDetail = { ...newVal }
      },
      immediate: true,
      deep: true
    },
    tempType: {
      handler(newVal) {
        this.localTempType = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      // 参数校验
      if (this.localTempType == 1) {
        if (
          this.localTempDetail.start1 > this.localTempDetail.end1 ||
          this.localTempDetail.start2 > this.localTempDetail.end2 ||
          this.localTempDetail.start3 > this.localTempDetail.end3
        ) {
          this.$modal.msg("结束时间需大于等于起始时间！");
          return;
        }
      }
      
      this.$emit('confirm', {
        tempDetail: this.localTempDetail,
        tempType: this.localTempType
      });
    },
    handlePickerTimes(type, index) {
      this.$emit('pickerTimes', type, index);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.temp {
  padding: 20rpx;
}

.lab {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
  line-height: 1.5;
}

.date-container {
  border: 1rpx solid #e4e7ed;
  border-radius: 6rpx;
  margin: 0 20rpx;
  background: #f8f9fa;
}

.time-box {
  padding: 10rpx 15rpx;
  font-size: 28rpx;
  color: #333;
}

.to {
  font-size: 24rpx;
  color: #666;
  margin: 0 10rpx;
}

.temp-item {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.marleft {
  margin-left: 20rpx;
}
</style>
