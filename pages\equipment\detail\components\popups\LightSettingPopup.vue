<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="灯光设置">
    <view class="popup-content temp">
      <view
        class="flex align-center justify-between"
        style="margin-bottom: 24rpx">
        <view class="lab" style="margin: 0 24rpx 0 0">是否打开:</view>
        <u-switch v-model="localLightDetail.lightControl"></u-switch>
      </view>

      <view class="info">时段设置为X点至Y点，结束时间需大于起始时间</view>
      
      <view class="flex align-center">
        <view class="lab">时段1:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="handlePickerTimes('light', 0)">
          <view class="time-box-l">{{ localLightDetail.start1 }}</view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ localLightDetail.end1 }}</view>
        </view>
      </view>
      
      <view class="flex align-center">
        <view class="lab">时段2:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="handlePickerTimes('light', 1)">
          <view class="time-box-l">{{ localLightDetail.start2 }}</view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ localLightDetail.end2 }}</view>
        </view>
      </view>
      
      <view class="flex align-center">
        <view class="lab">时段3:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="handlePickerTimes('light', 2)">
          <view class="time-box-l">{{ localLightDetail.start3 }}</view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ localLightDetail.end3 }}</view>
        </view>
      </view>
      
      <view style="height: 20rpx"></view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'LightSettingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    lightDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localLightDetail: {}
    }
  },
  watch: {
    lightDetail: {
      handler(newVal) {
        this.localLightDetail = { ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.localLightDetail);
    },
    handlePickerTimes(type, index) {
      this.$emit('pickerTimes', type, index);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.temp {
  padding: 20rpx;
}

.lab {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}

.info {
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
  line-height: 1.5;
}

.date-container {
  border: 1rpx solid #e4e7ed;
  border-radius: 6rpx;
  margin: 0 20rpx;
  background: #f8f9fa;
}

.time-box-l {
  padding: 10rpx 15rpx;
  font-size: 28rpx;
  color: #333;
}

.to {
  font-size: 24rpx;
  color: #666;
  margin: 0 10rpx;
}

.marleft {
  margin-left: 20rpx;
}
</style>
