<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="加热丝设置">
    <view class="popup-content temp">
      <view
        class="flex align-center justify-between"
        style="margin-bottom: 24rpx">
        <view class="lab" style="margin: 0 24rpx 0 0">是否冷冻机:</view>
        <view :class="[localJrsDetail.isIce ? 'green' : 'red']">
          {{ localJrsDetail.isIce ? "是" : "否" }}
        </view>
      </view>

      <view
        class="flex align-center justify-between"
        style="margin-bottom: 24rpx"
        @click="handleJrsClick">
        <view class="lab" style="margin: 0 24rpx 0 0">开启玻璃加热除雾:</view>
        <u-switch
          :disabled="!localJrsDetail.hasJrs"
          v-model="localJrsDetail.enable">
        </u-switch>
      </view>

      <block v-if="localJrsDetail.enable">
        <view class="info">时段设置为X点至Y点，结束时间需大于起始时间</view>
        
        <view class="flex align-center">
          <view class="lab">时段1:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('jrs', 0)">
            <view class="time-box-l">{{ localJrsDetail.start1 }}</view>
            <view class="lab to">至</view>
            <view class="time-box-l">{{ localJrsDetail.end1 }}</view>
          </view>
        </view>
        
        <view class="flex align-center">
          <view class="lab">时段2:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('jrs', 1)">
            <view class="time-box-l">{{ localJrsDetail.start2 }}</view>
            <view class="lab to">至</view>
            <view class="time-box-l">{{ localJrsDetail.end2 }}</view>
          </view>
        </view>
        
        <view class="flex align-center">
          <view class="lab">时段3:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="handlePickerTimes('jrs', 2)">
            <view class="time-box-l">{{ localJrsDetail.start3 }}</view>
            <view class="lab to">至</view>
            <view class="time-box-l">{{ localJrsDetail.end3 }}</view>
          </view>
        </view>
      </block>
      
      <view style="height: 20rpx"></view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'HeaterSettingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    jrsDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localJrsDetail: {}
    }
  },
  watch: {
    jrsDetail: {
      handler(newVal) {
        this.localJrsDetail = { ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleConfirm() {
      // 参数校验
      if (this.localJrsDetail.enable) {
        if (
          this.localJrsDetail.start1 > this.localJrsDetail.end1 ||
          this.localJrsDetail.start2 > this.localJrsDetail.end2 ||
          this.localJrsDetail.start3 > this.localJrsDetail.end3
        ) {
          this.$modal.msg("结束时间需大于等于起始时间！");
          return;
        }
      }
      
      this.$emit('confirm', this.localJrsDetail);
    },
    handlePickerTimes(type, index) {
      this.$emit('pickerTimes', type, index);
    },
    handleJrsClick() {
      this.$emit('jrsClick');
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.temp {
  padding: 20rpx;
}

.lab {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}

.info {
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
  line-height: 1.5;
}

.date-container {
  border: 1rpx solid #e4e7ed;
  border-radius: 6rpx;
  margin: 0 20rpx;
  background: #f8f9fa;
}

.time-box-l {
  padding: 10rpx 15rpx;
  font-size: 28rpx;
  color: #333;
}

.to {
  font-size: 24rpx;
  color: #666;
  margin: 0 10rpx;
}

.marleft {
  margin-left: 20rpx;
}

.green {
  color: #52c41a;
  font-weight: bold;
}

.red {
  color: #ff4d4f;
  font-weight: bold;
}
</style>
